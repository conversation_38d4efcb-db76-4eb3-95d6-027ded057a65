<div class="widget-products-with-category py-5 bg-light">
    <div class="container-xxxl">
        <div class="row">
            <div class="col-12">
                <div class="text-center mb-5">
                    <h2 class="more-to-love-title">{{ __('More to Love') }}</h2>
                </div>
                <div class="product-deals-day__body arrows-top-right">
                    <div class="product-deals-day-body more-to-love-products-grid">
                        @foreach ($products as $product)
                            <div class="product-inner bg-white">
                                {!! Theme::partial('ecommerce.product-item-grid', ['product' => $product, 'wishlistIds' => $wishlistIds ?? []]) !!}
                            </div>
                        @endforeach
                    </div>
                    @if ($products->count() >= 30)
                        <div class="text-center mt-4 mb-5 pb-5">
                            <button class="load-more-products"
                                data-page="1"
                                data-loading-text="{{ __('Loading...') }}">
                                <span class="button-text">{{ __('VIEW MORE') }}</span>
                                <i class="fas fa-chevron-down button-icon"></i>
                            </button>
                        </div>
                    @endif
                    <div class="end-of-products d-none mt-3 mb-5 pb-5 text-center">
                        {{ __('You have reached the end') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .more-to-love-title {
        font-size: 32px;
        font-weight: 700;
        margin-bottom: 0;
        color: #000;
    }

    .more-to-love-products-grid {
        display: grid;
        grid-template-columns: repeat(6, 1fr);
        gap: 1rem;
    }

    @media (max-width: 1400px) {
        .more-to-love-products-grid {
            grid-template-columns: repeat(5, 1fr);
        }
    }

    @media (max-width: 1199px) {
        .more-to-love-products-grid {
            grid-template-columns: repeat(4, 1fr);
        }
    }

    @media (max-width: 1024px) {
        .more-to-love-products-grid {
            grid-template-columns: repeat(3, 1fr);
        }
        .more-to-love-title {
            font-size: 28px;
        }
    }

    @media (max-width: 767px) {
        .more-to-love-products-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.5rem;
        }
        .more-to-love-title {
            font-size: 24px;
        }

        /* Reset any existing alignment styles */
        .product-inner,
        .product-inner *,
        .product-details,
        .product-details * {
            text-align: left !important;
        }

        /* Target specific elements */
        .product-inner .product-details .product-content-box {
            padding: 15px !important;
            text-align: left !important;
        }

        .product-inner .product-details .product-content-box .sold-by-meta {
            text-align: left !important;
            margin-bottom: 5px !important;
        }

        .product-inner .product-details .product-content-box .sold-by-meta a {
            text-align: left !important;
            display: inline-block !important;
        }
    }

    .product-inner {
        width: 100%;
        border-radius: 4px;
        overflow: hidden;
        padding: 10px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }

    .load-more-products {
        padding: 10px 30px;
        font-weight: 500;
        background-color: #ff6633;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
    }

    .load-more-products:hover {
        background-color: #e55a2d;
    }

    .load-more-products .button-text {
        margin-right: 8px;
    }

    .load-more-products .button-icon {
        font-size: 12px;
    }

    .load-more-products.loading .button-icon {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .end-of-products {
        color: #666;
        font-size: 14px;
    }

    /* Add extra space for mobile sticky bar */
    @media (max-width: 991px) {
        .load-more-products, .end-of-products {
            margin-bottom: 80px !important;
            padding-bottom: 20px !important;
        }
    }
</style>

<script>
    window.addEventListener('load', function() {
        $(document).ready(function() {
            let isLoading = false;
            let shownProductIds = [];

            // Collect initially shown product IDs
            $('.more-to-love-products-grid .product-inner').each(function() {
                const productId = $(this).find('.product-thumbnail[data-product-id]').data('product-id');
                if (productId) {
                    shownProductIds.push(productId);
                }
            });

            $('.load-more-products').on('click', function() {
                if (isLoading) return;

                const button = $(this);
                const page = parseInt(button.data('page'));
                const loadingText = button.data('loading-text');

                isLoading = true;
                button.addClass('loading');
                button.html(`<span class="button-text">${loadingText.toUpperCase()}</span> <i class="fas fa-spinner button-icon"></i>`);

                $.ajax({
                    url: '{{ route('public.ajax.more-to-love') }}',
                    method: 'GET',
                    data: {
                        page: page + 1,
                        shown_ids: shownProductIds.join(',')
                    },
                    success: function(response) {
                        if (response.data && response.data.data && response.data.data.products && response.data.data.products.length > 0) {
                            const productsHtml = response.data.data.products.join('');
                            $('.more-to-love-products-grid').append(productsHtml);
                            button.data('page', page + 1);

                            // Add new product IDs to the shown list
                            if (response.data.data.new_product_ids) {
                                shownProductIds = shownProductIds.concat(response.data.data.new_product_ids);
                            }

                            // Initialize lazy loading for new images
                            if (typeof MartApp !== 'undefined') {
                                MartApp.lazyLoad($('.more-to-love-products-grid')[0]);
                            } else if (typeof LazyLoad !== 'undefined') {
                                new LazyLoad({
                                    container: $('.more-to-love-products-grid')[0],
                                    elements_selector: '.lazyload',
                                    callback_error: (img) => {
                                        img.setAttribute('src', siteConfig?.img_placeholder || '');
                                    }
                                });
                            }

                            if (response.data.data.has_more === false) {
                                button.parent().remove();
                                $('.end-of-products').removeClass('d-none');
                                if (response.data.data.message) {
                                    $('.end-of-products').text(response.data.data.message);
                                }
                            }
                        } else {
                            button.parent().remove();
                            $('.end-of-products').removeClass('d-none');
                            if (response.data && response.data.data && response.data.data.message) {
                                $('.end-of-products').text(response.data.data.message);
                            } else {
                                $('.end-of-products').text('{{ __('No more products available') }}');
                            }
                        }
                    },
                    error: function() {
                        isLoading = false;
                        button.removeClass('loading');
                        button.html(`<span class="button-text">{{ __('VIEW MORE') }}</span> <i class="fas fa-chevron-down button-icon"></i>`);
                    },
                    complete: function() {
                        isLoading = false;
                        button.removeClass('loading');
                        button.html(`<span class="button-text">{{ __('VIEW MORE') }}</span> <i class="fas fa-chevron-down button-icon"></i>`);
                    }
                });
            });

            // Add extra padding when sticky bar is visible
            function adjustViewMoreButtonPadding() {
                if (window.innerWidth <= 991) {
                    const stickyBar = document.querySelector('.mobile-sticky-add-to-cart');
                    const viewMoreButton = document.querySelector('.load-more-products');
                    const endMessage = document.querySelector('.end-of-products');

                    if (stickyBar) {
                        const stickyBarHeight = stickyBar.offsetHeight;

                        if (viewMoreButton) {
                            viewMoreButton.closest('div').style.marginBottom = (stickyBarHeight + 20) + 'px';
                            viewMoreButton.closest('div').style.paddingBottom = '20px';
                        }

                        if (endMessage && !endMessage.classList.contains('d-none')) {
                            endMessage.style.marginBottom = (stickyBarHeight + 20) + 'px';
                            endMessage.style.paddingBottom = '20px';
                        }
                    }
                }
            }

            // Run on page load
            adjustViewMoreButtonPadding();

            // Run on window resize
            window.addEventListener('resize', adjustViewMoreButtonPadding);
        });
    });
</script>
