<div class="recently-product-wrapper">
    <?php if($products->isNotEmpty()): ?>
        <ul
            class="product-list"
            data-slick="<?php echo e(json_encode(['arrows' => true, 'dots' => false, 'autoplay' => false, 'infinite' => true, 'slidesToShow' => 10])); ?>"
        >
            <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="product">
                    <a href="<?php echo e($product->url); ?>">
                        <img
                            src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>"
                            alt="<?php echo e($product->name); ?>"
                        />
                    </a>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php else: ?>
        <div class="recently-empty-products text-center">
            <div class="empty-desc">
                <span><?php echo e(__('Recently Viewed Products is a function which helps you keep track of your recent viewing history.')); ?></span>
                <a
                    class="text-primary"
                    href="<?php echo e(route('public.products')); ?>"
                ><?php echo e(__('Shop Now')); ?></a>
            </div>
        </div>
    <?php endif; ?>
    <div>
<?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/ecommerce/recently-viewed-products.blade.php ENDPATH**/ ?>