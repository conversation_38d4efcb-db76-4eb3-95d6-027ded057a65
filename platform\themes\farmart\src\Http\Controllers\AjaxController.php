<?php

namespace Theme\Farmart\Http\Controllers;

use Botble\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON>ble\Theme\Http\Controllers\PublicController;
use Illuminate\Http\Request;
use Theme\Farmart\Supports\Wishlist;

class AjaxController extends PublicController
{
    /**
     * Get more products for the "More to Love" section
     *
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function getMoreToLoveProducts(Request $request, BaseHttpResponse $response)
    {
        $page = (int)$request->input('page', 1);
        $perPage = 30;
        $offset = ($page - 1) * $perPage;

        // Get the current product ID from the request (if available)
        $currentProductId = (int)$request->input('product_id', 0);

        // Get already shown product IDs to prevent duplicates
        $shownProductIds = $request->input('shown_ids', []);
        if (is_string($shownProductIds)) {
            $shownProductIds = explode(',', $shownProductIds);
        }
        $shownProductIds = array_filter(array_map('intval', $shownProductIds));

        $condition = [
            'ec_products.status' => \Botble\Base\Enums\BaseStatusEnum::PUBLISHED,
            'ec_products.is_variation' => 0, // Exclude variation products
        ];

        // Exclude current product if ID is provided
        if ($currentProductId > 0) {
            $condition[] = ['ec_products.id', '!=', $currentProductId];
        }

        // Exclude already shown products to prevent duplicates
        if (!empty($shownProductIds)) {
            $condition[] = ['ec_products.id', 'NOT IN', $shownProductIds];
        }

        $products = get_products([
            'condition' => $condition,
            'take' => $perPage + 1, // Take one more to check if there are more
            'skip' => $offset,
            'with' => [
                'slugable',
                'variations',
                'productLabels',
                'variationAttributeSwatchesForProductList',
                'productCollections',
            ],
            'order_by' => ['created_at' => 'DESC'],
        ]);

        $hasMore = $products->count() > $perPage;

        // Remove the extra product if there are more
        if ($hasMore) {
            $products = $products->take($perPage);
        }

        // If no products found, return empty response
        if ($products->isEmpty()) {
            return $response->setData([
                'data' => [
                    'products' => [],
                    'has_more' => false,
                    'message' => __('No more products available'),
                ]
            ]);
        }

        $wishlistIds = Wishlist::getWishlistIds($products->pluck('id')->all());

        $data = [];
        $newProductIds = [];
        foreach ($products as $product) {
            $data[] = '<div class="product-inner bg-white">' . Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) . '</div>';
            $newProductIds[] = $product->id;
        }

        return $response->setData([
            'data' => [
                'products' => $data,
                'has_more' => $hasMore,
                'new_product_ids' => $newProductIds,
                'message' => $hasMore ? null : __('No more products available'),
            ]
        ]);
    }
}
