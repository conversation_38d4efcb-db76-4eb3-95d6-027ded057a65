<?php

namespace Theme\Farmart\Http\Controllers;

use Botble\Base\Http\Responses\BaseHttpResponse;
use <PERSON><PERSON>ble\Theme\Facades\Theme;
use Bo<PERSON>ble\Theme\Http\Controllers\PublicController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Theme\Farmart\Supports\Wishlist;

class AjaxController extends PublicController
{
    /**
     * Get more products for the "More to Love" section
     *
     * @param Request $request
     * @param BaseHttpResponse $response
     * @return BaseHttpResponse
     */
    public function getMoreToLoveProducts(Request $request, BaseHttpResponse $response)
    {
        $page = (int)$request->input('page', 1);
        $perPage = 30;

        // Get the current product ID from the request (if available)
        $currentProductId = (int)$request->input('product_id', 0);

        // Get already shown product IDs to prevent duplicates in current session
        $shownProductIds = $request->input('shown_ids', []);
        if (is_string($shownProductIds)) {
            $shownProductIds = explode(',', $shownProductIds);
        }
        $shownProductIds = array_filter(array_map('intval', $shownProductIds));

        $condition = [
            'ec_products.status' => \Botble\Base\Enums\BaseStatusEnum::PUBLISHED,
            'ec_products.is_variation' => 0, // Exclude variation products
        ];

        // Exclude current product if ID is provided
        if ($currentProductId > 0) {
            $condition[] = ['ec_products.id', '!=', $currentProductId];
        }

        // First, check if we have exhausted all unique products
        $totalAvailableProducts = get_products([
            'condition' => $condition,
            'take' => null, // Get count of all available products
            'with' => [],
        ]);

        // If we've shown all available products, reset and start over
        $resetCycle = false;
        if (!empty($shownProductIds) && count($shownProductIds) >= $totalAvailableProducts->count()) {
            $shownProductIds = []; // Reset to show all products again
            $resetCycle = true;
        }

        // Exclude already shown products to prevent duplicates in current cycle
        // But only if we haven't just reset the cycle
        if (!empty($shownProductIds) && !$resetCycle) {
            $condition[] = ['ec_products.id', 'NOT IN', $shownProductIds];
        }

        // Get products and shuffle them
        $allAvailableProducts = get_products([
            'condition' => $condition,
            'take' => null, // Get all available products
            'with' => [
                'slugable',
                'variations',
                'productLabels',
                'variationAttributeSwatchesForProductList',
                'productCollections',
            ],
            'order_by' => ['ec_products.created_at' => 'DESC'],
        ]);

        // Shuffle and take the required amount + 1 to check if there are more
        $products = $allAvailableProducts->shuffle()->take($perPage + 1);

        $hasMore = $products->count() > $perPage;

        // Remove the extra product if there are more
        if ($hasMore) {
            $products = $products->take($perPage);
        }

        // If no products found, return empty response
        if ($products->isEmpty()) {
            return $response->setData([
                'data' => [
                    'products' => [],
                    'has_more' => false,
                    'message' => __('No more products available'),
                ]
            ]);
        }

        $wishlistIds = Wishlist::getWishlistIds($products->pluck('id')->all());

        $data = [];
        $newProductIds = [];
        foreach ($products as $product) {
            $data[] = '<div class="product-inner bg-white">' . Theme::partial('ecommerce.product-item-grid', compact('product', 'wishlistIds')) . '</div>';
            $newProductIds[] = $product->id;
        }

        return $response->setData([
            'data' => [
                'products' => $data,
                'has_more' => $hasMore,
                'new_product_ids' => $newProductIds,
                'reset_cycle' => $resetCycle,
                'message' => $hasMore ? null : ($resetCycle ? __('Showing all products again!') : __('No more products available')),
            ]
        ]);
    }
}
