$(document).ready(function() {
    let isLoading = false;
    let shownProductIds = [];

    // Collect initially shown product IDs
    $('.more-to-love-products-grid .product-inner').each(function() {
        const productId = $(this).find('.product-thumbnail[data-product-id]').data('product-id');
        if (productId) {
            shownProductIds.push(productId);
        }
    });

    $('.load-more-products').on('click', function() {
        if (isLoading) return;

        const button = $(this);
        const page = parseInt(button.data('page'));
        const loadingText = button.data('loading-text');

        isLoading = true;
        button.addClass('loading');
        button.html(`<span class="button-text">${loadingText.toUpperCase()}</span> <i class="fas fa-spinner button-icon"></i>`);

        $.ajax({
            url: route('public.ajax.more-to-love'),
            method: 'GET',
            data: {
                page: page + 1,
                shown_ids: shownProductIds.join(',')
            },
            success: function(response) {
                if (response.data && response.data.data && response.data.data.products && response.data.data.products.length > 0) {
                    const productsHtml = response.data.data.products.join('');
                    $('.more-to-love-products-grid').append(productsHtml);
                    button.data('page', page + 1);

                    // Add new product IDs to the shown list
                    if (response.data.data.new_product_ids) {
                        shownProductIds = shownProductIds.concat(response.data.data.new_product_ids);
                    }

                    // Initialize lazy loading for new images
                    if (typeof MartApp !== 'undefined') {
                        MartApp.lazyLoad($('.more-to-love-products-grid')[0]);
                    } else if (typeof LazyLoad !== 'undefined') {
                        new LazyLoad({
                            container: $('.more-to-love-products-grid')[0],
                            elements_selector: '.lazyload',
                            callback_error: (img) => {
                                img.setAttribute('src', siteConfig?.img_placeholder || '');
                            }
                        });
                    }

                    if (response.data.data.has_more === false) {
                        button.parent().remove();
                        $('.end-of-products').removeClass('d-none');
                        if (response.data.data.message) {
                            $('.end-of-products').text(response.data.data.message);
                        }
                    }
                } else {
                    button.parent().remove();
                    $('.end-of-products').removeClass('d-none');
                    if (response.data && response.data.data && response.data.data.message) {
                        $('.end-of-products').text(response.data.data.message);
                    } else {
                        $('.end-of-products').text('No more products available');
                    }
                }
            },
            error: function() {
                isLoading = false;
                button.removeClass('loading');
                button.html(`<span class="button-text">VIEW MORE</span> <i class="fas fa-chevron-down button-icon"></i>`);
            },
            complete: function() {
                isLoading = false;
                button.removeClass('loading');
                button.html(`<span class="button-text">VIEW MORE</span> <i class="fas fa-chevron-down button-icon"></i>`);
            }
        });
    });

    // Add extra padding when sticky bar is visible
    function adjustViewMoreButtonPadding() {
        if (window.innerWidth <= 991) {
            const stickyBar = document.querySelector('.mobile-sticky-add-to-cart');
            const viewMoreButton = document.querySelector('.load-more-products');
            const endMessage = document.querySelector('.end-of-products');

            if (stickyBar) {
                const stickyBarHeight = stickyBar.offsetHeight;

                if (viewMoreButton) {
                    viewMoreButton.closest('div').style.marginBottom = (stickyBarHeight + 20) + 'px';
                    viewMoreButton.closest('div').style.paddingBottom = '20px';
                }

                if (endMessage && !endMessage.classList.contains('d-none')) {
                    endMessage.style.marginBottom = (stickyBarHeight + 20) + 'px';
                    endMessage.style.paddingBottom = '20px';
                }
            }
        }
    }

    // Run on page load
    adjustViewMoreButtonPadding();

    // Run on window resize
    window.addEventListener('resize', adjustViewMoreButtonPadding);
});
