<div class="product-thumbnail" data-product-id="<?php echo e($product->id); ?>">
    <a
        class="product-loop__link img-fluid-eq"
        href="<?php echo e($product->url); ?>"
        tabindex="0"
    >
        <div class="img-fluid-eq__dummy"></div>
        <div class="img-fluid-eq__wrap">
            <img
                class="lazyload product-thumbnail__img"
                data-src="<?php echo e(RvMedia::getImageUrl($product->image, 'small', false, RvMedia::getDefaultImage())); ?>"
                src="<?php echo e(image_placeholder($product->image, 'small')); ?>"
                alt="<?php echo e($product->name); ?>"
            >
        </div>
        <span class="ribbons">
            <?php if($product->isOutOfStock()): ?>
                <span class="ribbon out-stock"><?php echo e(__('Out Of Stock')); ?></span>
            <?php else: ?>
                <?php if($product->productLabels->isNotEmpty()): ?>
                    <?php $__currentLoopData = $product->productLabels; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <span
                            class="ribbon"
                            <?php if($label->color): ?> style="background-color: <?php echo e($label->color); ?>" <?php endif; ?>
                        ><?php echo e($label->name); ?></span>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <?php if($product->front_sale_price !== $product->price): ?>
                        <div
                            class="featured ribbon"
                            dir="ltr"
                        ><?php echo e(get_sale_percentage($product->price, $product->front_sale_price)); ?></div>
                    <?php endif; ?>
                <?php endif; ?>
            <?php endif; ?>
        </span>
    </a>
</div>
<div class="product-details position-relative" style="padding: 0;">
    <!-- Price Tag -->
    <div style="background-color: #ff6633; margin: 0; padding: 10px 15px; width: 100%; position: relative; color: #fff;">
        <?php if($product->front_sale_price !== $product->price): ?>
            <div style="display: flex; align-items: center; justify-content: flex-start; height: 40px;">
                <div style="display: flex; flex-direction: column; justify-content: center;">
                    <span style="font-size: 18px; font-weight: 700; color: #fff; line-height: 1.2; text-align: left;"><?php echo e(format_price($product->front_sale_price_with_taxes)); ?></span>
                    <span style="font-size: 14px; text-decoration: line-through; opacity: 0.8; color: #fff; line-height: 1.2; text-align: left;"><?php echo e(format_price($product->price_with_taxes)); ?></span>
                </div>
            </div>
        <?php else: ?>
            <div style="display: flex; align-items: center; justify-content: flex-start; height: 40px;">
                <span style="font-size: 18px; font-weight: 700; color: #fff; text-align: left;"><?php echo e(format_price($product->front_sale_price_with_taxes)); ?></span>
            </div>
        <?php endif; ?>
    </div>

    <div class="product-content-box" style="padding: 15px;">
        <?php if(is_plugin_active('marketplace') && $product->store->id): ?>
            <div class="sold-by-meta">
                <a
                    href="<?php echo e($product->store->url); ?>"
                    tabindex="0"
                    style="color: #000080 !important; border: 1px solid #000080; border-radius: 4px; padding: 2px 8px; display: inline-block; margin-bottom: 5px; text-decoration: none; font-size: 12px; font-weight: 500; transition: all 0.3s ease; background-color: rgba(0, 0, 128, 0.05);"
                    onmouseover="this.style.color='#ff6633'; this.style.borderColor='#ff6633'; this.style.backgroundColor='rgba(255, 102, 51, 0.05)'; this.style.boxShadow='0 2px 4px rgba(0, 0, 0, 0.1)';"
                    onmouseout="this.style.color='#000080'; this.style.borderColor='#000080'; this.style.backgroundColor='rgba(0, 0, 128, 0.05)'; this.style.boxShadow='none';"
                ><?php echo e($product->store->name); ?></a>
            </div>
        <?php endif; ?>
        <h3 class="product__title" style="color: #000000 !important;">
            <a
                href="<?php echo e($product->url); ?>"
                tabindex="0"
                style="color: #000000 !important; font-weight: 500;"
                onmouseover="this.style.color='#ff6633'"
                onmouseout="this.style.color='#000000'"
            ><?php echo e($product->name); ?></a>
        </h3>
        <?php if(EcommerceHelper::isReviewEnabled()): ?>
            <?php echo Theme::partial('star-rating', ['avg' => $product->reviews_avg, 'count' => $product->reviews_count]); ?>

        <?php endif; ?>
    </div>
</div><?php /**PATH C:\Users\<USER>\Desktop\uu\platform\themes/farmart/partials/ecommerce/product-item-grid.blade.php ENDPATH**/ ?>